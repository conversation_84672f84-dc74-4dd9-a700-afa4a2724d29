const https = require('https');

// 配置请求参数
const options = {
    hostname: '*************',
    path: '/U9C/webapi/CommonEntity/Query',
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJFbnRlcnByaXNlSUQiOiIxMDIiLCJFbnRDb2RlIjoiMTAyIiwiQ3VsdHVyZU5hbWUiOiJ6aC1DTiIsIk9yZ0NvZGUiOiIxMDEiLCJPcmdJRCI6IjEwMDIzMDYwMTAxMTAwMjciLCJPcmdOYW1lIjoiMTAwMjMwNjAxMDExMDI3MCIsIlVzZXJJRCI6IjEwMDIzMDYwMTAxMTAyNzAiLCJVc2VyQ29kZSI6ImFkbWluIiwiTG9naW5EYXRlIjpudWxsLCJVc2VyTmFtZSI6ImFkbWluIiwiRXhwaXJlIjoiMjAyNS0wNy0yNlQyMDoxNDo1OC4yMDUzODMzKzA4OjAwIiwiVmFsaWRBdWRpZW5jZSI6IjEwMjEwMWFkbWluMTc1MzU0NjQ5ODIwNSIsIkVmZmVjdGl2ZU1pbiI6MjQwLCJBcHBJRCI6Im9kb28iLCJUb2tlbkV4dGVuZEtleSI6ImFkbWluQDEwMUAxMDJAb2RvbyJ9.cMwq21EAc5-Z0HoK3AdBfoLnqjg6FkVR7SwThXa2dbc'
    }
};

// 请求体数据
const requestBody = {
    "PageSize": 10,
    "PageIndex": 1,
    "Orders": "ID desc",
    "Filters": [
        {
            "Field": "SrcBIPSODocNo",
            "Operator": "eq",
            "Value": "SO2506270095",
            "Logic": "and"
        }
    ],
    "EntityFullName": "UFIDA::U9::SM::SO::SO",
    "ReturnFields": [
        "ID",
        "ReleaseMen"
    ]
};

// 发送请求的函数
function sendRequest() {
    return new Promise((resolve, reject) => {
        const req = https.request(options, (res) => {
            let data = '';

            // 接收数据
            res.on('data', (chunk) => {
                data += chunk;
            });

            // 请求完成
            res.on('end', () => {
                console.log('状态码:', res.statusCode);
                console.log('响应头:', res.headers);
                console.log('响应数据:', data);
                
                try {
                    const jsonData = JSON.parse(data);
                    resolve(jsonData);
                } catch (error) {
                    console.log('响应不是有效的JSON格式');
                    resolve(data);
                }
            });
        });

        // 错误处理
        req.on('error', (error) => {
            console.error('请求错误:', error);
            reject(error);
        });

        // 发送请求体
        req.write(JSON.stringify(requestBody));
        req.end();
    });
}

// 执行请求
async function main() {
    try {
        console.log('正在发送请求到:', `https://${options.hostname}${options.path}`);
        console.log('请求体:', JSON.stringify(requestBody, null, 2));
        
        const response = await sendRequest();
        console.log('\n请求成功完成!');
        
        // 如果响应是JSON格式，可以进一步处理
        if (typeof response === 'object') {
            console.log('\n解析后的响应数据:');
            console.log(JSON.stringify(response, null, 2));
        }
        
    } catch (error) {
        console.error('请求失败:', error.message);
    }
}

// 运行主函数
main();
