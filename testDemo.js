const http = require('http');

const postData = JSON.stringify({
  "PageSize": 10,
  "PageIndex": 1,
  "Orders": "ID desc",
  "Filters": [
    {
      "Field": "OrderBy",
      "Operator": "eq",
      "Value": "Z1-0093",
      "Logic": "and"
    }
  ],
  "EntityFullName": "UFIDA::U9::SM::SO::SO",
  "ReturnFields": [
    "ID",
    "ReleaseMen"
  ]
});

const options = {
  hostname: '*************',
  path: '/U9C/webapi/CommonEntity/Query',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData),
    'token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJFbnRlcnByaXNlSUQiOiIxMDIiLCJFbnRDb2RlIjoiMTAyIiwiQ3VsdHVyZU5hbWUiOiJ6aC1DTiIsIk9yZ0NvZGUiOiIxMDEiLCJPcmdJRCI6IjEwMDIzMDYwMTAxMTAwMjciLCJPcmdOYW1lIjoiMTAwMjMwNjAxMDExMDI3MCIsIlVzZXJJRCI6IjEwMDIzMDYwMTAxMTAyNzAiLCJVc2VyQ29kZSI6ImFkbWluIiwiTG9naW5EYXRlIjpudWxsLCJVc2VyTmFtZSI6ImFkbWluIiwiRXhwaXJlIjoiMjAyNS0wNy0yNlQyMDoxNDo1OC4yMDUzODMzKzA4OjAwIiwiVmFsaWRBdWRpZW5jZSI6IjEwMjEwMWFkbWluMTc1MzU0NjQ5ODIwNSIsIkVmZmVjdGl2ZU1pbiI6MjQwLCJBcHBJRCI6Im9kb28iLCJUb2tlbkV4dGVuZEtleSI6ImFkbWluQDEwMUAxMDJAb2RvbyJ9.cMwq21EAc5-Z0HoK3AdBfoLnqjg6FkVR7SwThXa2dbc'
  }
};

const req = http.request(options, (res) => {
  console.log(`STATUS: ${res.statusCode}`);
  console.log(`HEADERS: ${JSON.stringify(res.headers)}`);
  
  res.setEncoding('utf8');
  let rawData = '';
  res.on('data', (chunk) => {
    rawData += chunk;
  });
  res.on('end', () => {
    try {
      const parsedData = JSON.parse(rawData);
      console.log('Response:', parsedData);
    } catch (e) {
      console.error('Parse error:', e.message);
      console.log('Raw response:', rawData);
    }
  });
});

req.on('error', (e) => {
  console.error(`Request error: ${e.message}`);
});

req.write(postData);
req.end();